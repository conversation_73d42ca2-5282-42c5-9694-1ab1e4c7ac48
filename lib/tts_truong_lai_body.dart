// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:io';

import 'package:animations/animations.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_tts/flutter_tts.dart';

import 'package:tts_truong_lai/value_notifier_list.dart';
import 'package:tts_truong_lai/theme/app_theme.dart';
import 'package:tts_truong_lai/widgets/custom_widgets.dart';
enum TtsState { playing, stopped, paused, continued }
class TTSTruongLaiBody extends StatefulWidget {
  const TTSTruongLaiBody({super.key, required this.total});
  final int total;
  @override
  State<TTSTruongLaiBody> createState() => _TTSTruongLaiBodyState();
}

class _TTSTruongLaiBodyState extends State<TTSTruongLaiBody> {
  late TextEditingController controller;
  late FlutterTts flutterTts;
   String? language;
  String? engine;
  double volume = 0.5;
  double pitch = 1.0;
  double rate = 0.5;
  bool isCurrentLanguageInstalled = false;
  ValueNotifier<TtsState?> vState = ValueNotifier(null);

  String? _newVoiceText;
  int? _inputLength;

  TtsState ttsState = TtsState.stopped;

  bool get isPlaying => ttsState == TtsState.playing;
  bool get isStopped => ttsState == TtsState.stopped;
  bool get isPaused => ttsState == TtsState.paused;
  bool get isContinued => ttsState == TtsState.continued;

  bool get isIOS => !kIsWeb && Platform.isIOS;
  bool get isAndroid => !kIsWeb && Platform.isAndroid;
  bool get isWindows => !kIsWeb && Platform.isWindows;
  bool get isWeb => kIsWeb;
  @override
  void initState() {
    _createStudentList(widget.total);
     initTts();
    _scrollController = ScrollController();
    controller = TextEditingController(text: '');
    super.initState();
  }
   dynamic initTts()async {
    flutterTts = FlutterTts();
    final ngonngu = await flutterTts.getLanguages;
    print(ngonngu); 
    await flutterTts.setLanguage("vi-VN");
    final ngonngu2 = await flutterTts.getLanguages;
     print(ngonngu2);
    _setAwaitOptions();

    if (isAndroid) {
      _getDefaultEngine();
      _getDefaultVoice();
    }

    flutterTts.setStartHandler(() {
      vState.value = TtsState.playing;
    });

    flutterTts.setCompletionHandler(() {
      vState.value = TtsState.stopped;
    });

    flutterTts.setCancelHandler(() {
      setState(() {
        print("Cancel");
        ttsState = TtsState.stopped;
      });
    });

    flutterTts.setPauseHandler(() {
      setState(() {
        print("Paused");
        ttsState = TtsState.paused;
      });
    });

    flutterTts.setContinueHandler(() {
      setState(() {
        print("Continued");
        ttsState = TtsState.continued;
      });
    });

    flutterTts.setErrorHandler((msg) {
      setState(() {
        print("error: $msg");
        ttsState = TtsState.stopped;
      });
    });
  }
  Future<void> _setAwaitOptions() async {
    await flutterTts.awaitSpeakCompletion(true);
  }
  Future<void> _getDefaultEngine() async {
    var engine = await flutterTts.getDefaultEngine;
    if (engine != null) {
      print("cong nghe: $engine");
    }
  }

  Future<void> _getDefaultVoice() async {
    var voice = await flutterTts.getDefaultVoice;
    if (voice != null) {
      print(voice);
    }
  }
   Future<void> _speak(final String text) async {
    await flutterTts.setVolume(volume);
    await flutterTts.setSpeechRate(rate);
    await flutterTts.setPitch(pitch);
    await flutterTts.speak(text);
     
  }
  ValueNotifierList<_Persion> students = ValueNotifierList([]);
  ValueNotifierList<_Persion> studentsAfter = ValueNotifierList([]);
  ValueNotifier<_Persion?> selectedStudents = ValueNotifier(null);
  int indexSelect = 0;
  late ScrollController _scrollController;
  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isLargeScreen = screenSize.width > 1200;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Hệ thống quản lý thi',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppTheme.cardColor,
        elevation: 0,
      ),
      body:  _buildStudentWaitingPanel(context)
      //  Padding(
      //   padding: EdgeInsets.all(24),
      //   child: Column(
      //     crossAxisAlignment: CrossAxisAlignment.start,
      //     children: [
      //       // Header Section with Input
           
      //       _buildStudentWaitingPanel(context)
      //       // Main Content
      //    //   _buildMainContent(context, isLargeScreen),
      //     ],
      //   ),
      // ),
  
    );
  }

  void _createStudentList(int total) {
    students.clear();
    for (var i = 1; i <= total; i++) {
      final sbd = i.toString().padLeft(total.toString().length, '0');
      StringBuffer buffer = StringBuffer();
      for (int j = 0; j < sbd.length; j++) {
        if (j > 0) buffer.write(' ');
        buffer.write(sbd[j]);
      }
      String result = buffer.toString();
      final person = _Persion(sbd, 'Xin mời thí sinh $result chuẩn bị thi', false, false);
      students.add(person);
    }
  }

  Widget _buildMainContent(BuildContext context, bool isLargeScreen) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 1000;

    if (isSmallScreen) {
      return Expanded(
        child: Column(
          children: [
            // Control Panel at top for small screens
            _buildControlPanel(context),
            const SizedBox(height: 16),
        Expanded(
            
              child: 
            //  _buildStudentWaitingPanel(context)
             _buildStudentAfterPanel(context)
              )
            // Students panels side by side
            // Column(
            //   children: [
            //     SizedBox(
            //       height: 600,
            //       child: _buildStudentWaitingPanel(context)),
            //     const SizedBox(width: 16),
            //     SizedBox(
            //       height: 600,
            //       child: _buildStudentAfterPanel(context)),
            //   ],
            // ),
          ],
        ),
      );
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left Panel - Students Waiting
        Expanded(
          flex: 2,
          child: _buildStudentWaitingPanel(context),
        ),

        const SizedBox(width: 24),

        // Center Panel - Current Student Control
        Expanded(
          flex: 1,
          child: _buildControlPanel(context),
        ),

        const SizedBox(width: 24),

        // Right Panel - Students After Exam
        Expanded(
          flex: 2,
          child: _buildStudentAfterPanel(context),
        ),
      ],
    );
  }

  Widget _buildStudentWaitingPanel(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ModernCard(
            padding: const EdgeInsets.all(0),
            child: Column(
              children: [
                SectionHeader(
                  title: 'Danh sách chờ thi',
                  subtitle: 'Thí sinh đang chờ được gọi thi',
                  action: ValueListenableBuilder(
                    valueListenable: students,
                    builder: (_, vStudents, __) => StatusBadge.info(
                      '${vStudents.length} thí sinh',
                      icon: Icons.people,
                    ),
                  ),
                ),
                Expanded(
                  child: ValueListenableBuilder(
                    valueListenable: students,
                    builder: (_, vStudents, child) {
                      if (vStudents.isEmpty) {
                        return EmptyState(
                          icon: Icons.people_outline,
                          title: 'Chưa có thí sinh',
                          subtitle: 'Vui lòng thiết lập số lượng thí sinh ở trên',
                        );
                      }
          
                      return ValueListenableBuilder(
                        valueListenable: selectedStudents,
                        builder: (context, vSelectedStudent, child) {
                          return ListView.builder(
                            controller: _scrollController,
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            itemCount: vStudents.length,
                            itemBuilder: (_, i) {
                              final item = vStudents[i];
                              final isSelected = vSelectedStudent?.id == item.id;
          
                              return ModernCard(
                                key: GlobalObjectKey(item),
                                isSelected: isSelected,
                                margin: const EdgeInsets.only(bottom: 8),
                                onTap: isSelected ? null : () {
                                  selectedStudents.value = item;
                                },
                                child: Row(
                                  children: [
                                    Container(
                                      width: 40,
                                      height: 40,
                                      decoration: BoxDecoration(
                                        color: isSelected
                                            ? AppTheme.primaryColor
                                            : AppTheme.textTertiary.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Center(
                                        child: Text(
                                          item.id,
                                          style: TextStyle(
                                            color: isSelected
                                                ? Colors.white
                                                : AppTheme.textSecondary,
                                            fontWeight: FontWeight.w600,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Thí sinh ${item.id}',
                                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            item.text,
                                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                              color: AppTheme.textSecondary,
                                            ),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ),
                                    if (item.isVang && !item.isDone)
                                      StatusBadge.warning('Vắng mặt')
                                    else if (!item.isVang && item.isDone)
                                      StatusBadge.success('Hoàn thành')
                                    else if (isSelected)
                                      Row(children: [
                                        TextButton(onPressed: (){
                                          _markStudentAbsent(item);
                                        }, child: Text('Vang mat')),
                                        
                                      ],)
                                    else
                                      SizedBox()
                                  ],
                                ),
                              );
                            },
                          );
                        },
                      );
                    },
                  ),
                ),
                _buildControlButtons(context),
              ],
            ),
          ),
        ),
         Expanded(child: _buildStudentAfterPanel(context))
      ],
    );
  }

  Widget _buildControlButtons(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: vState,
      builder: (context, valueState, child) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.selectedColor.withValues(alpha: 0.3),
            border: Border(
              top: BorderSide(color: AppTheme.borderColor),
            ),
          ),
          child: valueState == TtsState.playing 
          ? Expanded(
            child: ModernButton(
              enabled: valueState == TtsState.playing,
              text: 'Đang gọi thí sinh...',))
          : ValueListenableBuilder(
            valueListenable: selectedStudents,
            builder: (_, vStudent, child) {
              return Row(
                children: [
                  Expanded(
                    child: vStudent == null
                        ? ModernButton(
                            text: 'Bắt đầu thi',
                            icon: Icons.play_arrow,
                            onPressed:  () {
                              _startExam();
                            } ,
                          )
                        : ModernButton(
                            text: 'Thí sinh tiếp theo',
                            icon: Icons.skip_next,
                            onPressed: _hasNextStudent() ? () {
                              _nextStudent();
                            } : null,
                          ),
                  ),
                ],
              );
            },
          ),
        );
      }
    );
  }
   Widget _buildControlButton2(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.selectedColor.withOpacity(0.3),
        border: Border(
          top: BorderSide(color: AppTheme.borderColor),
        ),
      ),
      child: ValueListenableBuilder(
        valueListenable: studentsAfter,
        builder: (_, vStudentAfter, child) {
          return Row(
            children: [
              Expanded(
                child: vStudentAfter.isEmpty
                    ? ModernButton(
                      enabled: true,
                        text: 'Thi lại ',
                        icon: Icons.refresh,
                        onPressed:  null ,
                      )
                    : ModernButton(
                          text: 'Thi lại ',
                        icon: Icons.refresh,
                        onPressed:  () {
                          showDialog(context: context,
                           builder: (_){
                            return AlertDialog(
                              title: Text('Xác nhận'),
                              content: Text('Bạn có chắc chắn muốn thi lại không?'),
                              actions: [
                                TextButton(
                                  child: Text('Hủy'),
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                  },
                                ),
                                TextButton(
                                  child: Text('Xác nhận'),
                                  onPressed: () {
                                  // students.setValue([]);
                                    for (var element in studentsAfter.value) {
                                      element.isDone = false;
                                      element.isVang = false;
                                    }
                                    students.setValue(studentsAfter.value);
                                    studentsAfter.setValue([]);
                                    selectedStudents.value = null;
                                    indexSelect = 0;
                                    Navigator.of(context).pop();
                                  },
                                ),
                              ],
                            );
                          });
                        }   ,
                      ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildControlPanel(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: selectedStudents,
      builder: (_, vStudent, child) {
        return ModernCard(
          padding: const EdgeInsets.all(24),
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Current Student Display
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.primaryColor.withValues(alpha: .1),
                        AppTheme.secondaryColor.withValues(alpha: .1),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppTheme.primaryColor.withValues(alpha: .2)),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.person,
                        color: AppTheme.primaryColor,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        vStudent != null ? 'Thí sinh ${vStudent.id}' : 'Chưa chọn thí sinh',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppTheme.primaryColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      // Text(
                      //   vStudent?.text ?? 'Vui lòng chọn thí sinh để bắt đầu',
                      //   style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      //     color: AppTheme.textSecondary,
                      //   ),
                      //   textAlign: TextAlign.center,
                      // ),
                    ],
                  ),
                ),
            
                const SizedBox(height: 16),
            
                // Action Buttons
                if (vStudent != null) ...[
                  Row(
                    children: [
                      Expanded(
                        child: IconButton(
                          icon: Icon(Icons.person_off),
                          onPressed: () => _markStudentAbsent(vStudent),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: IconButton(
                          icon: Icon(Icons.check_circle),
                          onPressed: () => _markStudentPresent(vStudent),
                        ),
                      ),
                    ],
                  ),
                ] else ...[
                  ModernButton(
                    text: 'Chọn thí sinh đầu tiên',
                    icon: Icons.play_arrow,
                    onPressed: students.value.isNotEmpty ? () => _startExam() : null,
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStudentAfterPanel(BuildContext context) {
    return ModernCard(
      padding: const EdgeInsets.all(0),
      child: Column(
        children: [
          SectionHeader(
            title: 'Danh sách thi đợt sau',
           // subtitle: 'Thí sinh đã thi xong',
            action: ValueListenableBuilder(
              valueListenable: studentsAfter,
              builder: (_, vStudentsAfter, __) => StatusBadge.warning(
                '${vStudentsAfter.length} thí sinh',
                icon: Icons.check_circle,
              ),
            ),
          ),
          Expanded(
            child: ValueListenableBuilder(
              valueListenable: studentsAfter,
              builder: (_, vStudentsAfter, child) {
                if (vStudentsAfter.isEmpty) {
                  return EmptyState(
                    icon: Icons.check_circle_outline,
                    title: 'Chưa có thí sinh nào hoàn thành',
                    subtitle: 'Danh sách thí sinh đã thi sẽ hiển thị ở đây',
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: vStudentsAfter.length,
                  itemBuilder: (_, i) {
                    final item = vStudentsAfter[i];

                    return ModernCard(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: item.isVang
                                  ? AppTheme.warningColor
                                  : AppTheme.successColor,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Center(
                              child: Text(
                                item.id,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Thí sinh ${item.id}',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  item.isVang ? 'Vắng mặt' : 'Đã hoàn thành thi',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppTheme.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (item.isVang)
                            StatusBadge.warning('Vắng')
                          else
                            StatusBadge.success('Hoàn thành'),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          ),
          _buildControlButton2(context),
        ],
      ),
    );
  }

  // Helper Methods
  void _startExam() {
    if (students.value.isNotEmpty) {
      indexSelect = 0;
      selectedStudents.value = students.getItem(0);
      _scrollToStudent(students.getItem(0));
      _speak(students.getItem(0).text);
    }
  }

  void _nextStudent() {
    if (_hasNextStudent()) {
      indexSelect++;
      selectedStudents.value = students.getItem(indexSelect);
      _scrollToStudent(students.getItem(indexSelect));
      _speak(students.getItem(indexSelect).text);
    }
  }

  bool _hasNextStudent() {
    return indexSelect < students.value.length - 1;
  }

  void _scrollToStudent(_Persion student) {
    final context = GlobalObjectKey(student).currentContext;
    if (context != null) {
      Scrollable.ensureVisible(
        context,
        duration: const Duration(milliseconds: 800),
        curve: Curves.easeInOutCubic,
      );
    }
  }

  void _markStudentAbsent(_Persion student) {
   final newStudent = student.copyWith(isVang: true);
    studentsAfter.add(newStudent);
    final idxStudent = students.value.indexWhere((e) => e.id == student.id);
    if (idxStudent != -1) {
      students.updateValuebyIndex(
        idxStudent,
        student.copyWith(isVang: true),
      );
    }
 //   _nextStudent();
  }

  void _markStudentPresent(_Persion student) {
    studentsAfter.add(student);
    final idxStudent = students.value.indexWhere((e) => e.id == student.id);
    if (idxStudent != -1) {
      students.updateValuebyIndex(
        idxStudent,
        student.copyWith(isDone: true),
      );
    }
    _nextStudent();
  }
}

class _Persion {
  String id;
  String text;
  bool isDone = false;
  bool isVang = false;
  _Persion(this.id, this.text, this.isDone, this.isVang);

  _Persion copyWith({
    String? id,
    String? text,
    bool? isDone,
    bool? isVang,
  }) {
    return _Persion(
      id ?? this.id,
      text ?? this.text,
      isDone ?? this.isDone,
      isVang ?? this.isVang,
    );
  }
}
