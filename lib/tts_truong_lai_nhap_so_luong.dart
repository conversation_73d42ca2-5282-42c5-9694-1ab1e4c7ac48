import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tts_truong_lai/theme/app_theme.dart';
import 'package:tts_truong_lai/tts_truong_lai_body.dart';
import 'package:tts_truong_lai/widgets/custom_widgets.dart';

class TtsTruongLaiNhapSoLuong extends StatefulWidget {
  const TtsTruongLaiNhapSoLuong({super.key});

  @override
  State<TtsTruongLaiNhapSoLuong> createState() => _TtsTruongLaiNhapSoLuongState();
}

class _TtsTruongLaiNhapSoLuongState extends State<TtsTruongLaiNhapSoLuong> {
  late TextEditingController controller;
  @override
  void initState() {
    controller = TextEditingController(text: '');
    super.initState();
  }
  @override
  Widget build(final BuildContext context) {
    return Scaffold(
         appBar: AppBar(
        actions: [
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: () {
              showModal(context: context,
               builder: (builderContext){
                return AlertDialog(
                  title: Text('Cài đặt'),
                  content: SizedBox.square(
                    dimension: 400,
                    child: Card(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text('Cài đặt'),
                       //   Slider(value: value, onChanged: onChanged),
                          Text('Cài đặt'),
                        //  Slider(value: value, onChanged: onChanged),
                          Row(
                            children: [
                              Expanded(child: TextField()),
                               Card(),
                               Expanded(child: TextField()),
                            ],
                          ),
                         
                        ],
                      ),
                    ),
                  ),
                  actions: [
                    TextButton(
                      child: Text('OK'),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                    ),
                  ],
                );
              });
            }
          ),
      
        ],
      ),

      body: Center(
        child:  _buildHeaderSection(context),
      ),
    );
  }

   Widget _buildHeaderSection(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 800;

    return ModernCard(
      padding: const EdgeInsets.all(24),
      child: isSmallScreen
          ? Column(
            mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Thiết lập số lượng thí sinh',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  'Nhập tổng số thí sinh dự thi để bắt đầu',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(child: _buildInputNumber()),
                    const SizedBox(width: 16),
                    ModernButton(
                      text: 'Xác nhận',
                      icon: Icons.check,
                      onPressed: () => _showConfirmationDialog(context),
                    ),
                  ],
                ),
              ],
            )
          : Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Thiết lập số lượng thí sinh',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Nhập tổng số thí sinh dự thi để bắt đầu',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 24),
                SizedBox(
                  width: 200,
                  child: _buildInputNumber(),
                ),
                const SizedBox(width: 16),
                ModernButton(
                  text: 'Xác nhận',
                  icon: Icons.check,
                  onPressed: () => _showConfirmationDialog(context),
                ),
              ],
            ),
    );
  }

  Widget _buildInputNumber() {
    return ValueListenableBuilder(
      valueListenable: controller,
      builder: (context, value, child) {
        return TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          decoration: InputDecoration(
            labelText: 'Số lượng thí sinh',
            hintText: 'Nhập số lượng...',
            prefixIcon: Icon(Icons.people),
            suffixIcon: controller.text.isNotEmpty
                ? IconButton(
                    icon: Icon(Icons.clear),
                    onPressed: () {
                      controller.clear();
                    },
                  )
                : null,
          ),
        );
      },
    );
  }

    void _showConfirmationDialog(BuildContext context) {
    final total = int.tryParse(controller.text) ?? 0;
    if (total <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Vui lòng nhập số lượng thí sinh hợp lệ'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.group, color: AppTheme.primaryColor),
            const SizedBox(width: 12),
            Text('Xác nhận số lượng thí sinh'),
          ],
        ),
        content: Container(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Bạn có chắc chắn muốn tạo danh sách với:',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.selectedColor,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppTheme.primaryColor.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.people, color: AppTheme.primaryColor),
                    const SizedBox(width: 12),
                    Text(
                      '$total thí sinh',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          ModernButton(
            text: 'Hủy',
            isSecondary: true,
            onPressed: () => Navigator.of(context).pop(),
          ),
          ModernButton(
            text: 'Xác nhận',
            icon: Icons.check,
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => TTSTruongLaiBody(total:total),
                ),
              );
            
            
            },
          ),
        ],
      ),
    );
  }
}